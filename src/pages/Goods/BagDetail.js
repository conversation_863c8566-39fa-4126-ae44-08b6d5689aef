import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';
import { PlusOutlined, DoubleRightOutlined, DoubleLeftOutlined, EditOutlined, MoreOutlined, BarsOutlined } from '@ant-design/icons';

import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  ProDescriptions,
  ProFormText,
  ProFormTextArea,
  ProTable,
  ProFormSelect,
  ProFormUploadDragger,
  ProFormDigit,
  EditableProTable,
  ProCard,
  ProFormField,

} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Button, Drawer, Input, message, Dropdown, Space, Descriptions, InputNumber } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { request } from '@umijs/max';
import { centsToYuan, yuanToCents, formatPrice } from '@/util/price';
// import { editBag } from './Bag';


async function getData(options) {
  // TODO: 获取用户列表
  const { data } = await request('/admin/v1.0/luckybag/package/card/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });


  return {
    data: data.list,
    success: true,
    total: data.total,
  };
}


async function getBagDetail(options) {
  const res = await request('/admin/v1.0/luckybag/get', {
    method: 'GET',
    params: {
      ...(options || {}),
    },
  });
  return res;
}

async function add(options) {
  return request('/admin/v1.0/luckybag/package/card/create', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

async function edit(options) {
  return request('/admin/v1.0/luckybag/package/card/update', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });
}

async function getCardList(options) {
  const res = await request('/admin/v1.0/card/list', {
    method: 'POST',
    data: {
      ...(options || {}),
    },
  });

  return res.data.list;
}



const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  /// 在表格上显示了福袋名称，就不用每条都展示一个重复的福袋名称了
  // {
  //   title: '福袋名称',
  //   dataIndex: 'name',
  //   editable: false,
  // },
  {
    title: '赏品名称',
    dataIndex: 'card_name',
    width: '40%',
    editable: false,
  },
  {
    title: '售价',
    dataIndex: 'card_sold_price',
    sorter: true,
    editable: true,
    render: (text) => formatPrice(text),
    renderFormItem: (_, { record }) => (
      <InputNumber
        min={0}
        precision={2}
        placeholder="请输入价格(元)"
        defaultValue={centsToYuan(record?.card_sold_price)}
      />
    ),
  },
  {
    title: '成本',
    dataIndex: 'card_price',
    sorter: true,
    editable: true,
    render: (text) => formatPrice(text),
    renderFormItem: (_, { record }) => (
      <InputNumber
        min={0}
        precision={2}
        placeholder="请输入成本(元)"
        defaultValue={centsToYuan(record?.card_price)}
      />
    ),
  },
  {
    title: '数量',
    dataIndex: 'card_num',
    editable: true,
    render: (text) => Number(text) || 0,
    renderFormItem: () => <InputNumber min={0} precision={0} />,
  },
  {
    title: '已售',
    dataIndex: 'card_sold_num',
    editable: false,
  },
  {
    title: '创建时间',
    dataIndex: 'c_time',
    valueType: 'dateTime',
    editable: false,
  },
  {
    title: '更新时间',
    dataIndex: 'm_time',
    valueType: 'dateTime',
    sorter: true,
    editable: false,
  },
  {
    title: '操作',
    valueType: 'option',
    width: 150,
    render: (text, record, _, action) => [
      <a
        key="editable"
        onClick={() => {
          action?.startEditable?.(record.id);
        }}
      >
        编辑
      </a>,
    ],
  },
];

const BagDetail = () => {
  const [addVisible, setAddVisible] = useState(false);
  const [editableKeys, setEditableRowKeys] = useState([]);
  const [bagDetail, setBagDetail] = useState({});
  const [cardList, setCardList] = useState([]);
  const tableRef = useRef();
  const params = useParams();

  useEffect(() => {
    getBagDetail({ id: Number(params.id) }).then(res => {
      setBagDetail(res?.data || res);
    });
  }, [params.id]);

  return (
    <PageContainer
      content={
        <Descriptions column={3}>
          <Descriptions.Item label="福袋单价">{formatPrice(bagDetail.bag_price)}元</Descriptions.Item>
          <Descriptions.Item label="福袋数量">{bagDetail.bag_count}</Descriptions.Item>
          <Descriptions.Item label="福袋总价">{formatPrice(bagDetail.bag_price * bagDetail.bag_count)}元</Descriptions.Item>
          <Descriptions.Item label="卡总售价">{formatPrice(bagDetail.bag_price * bagDetail.bag_count)}元</Descriptions.Item>
          <Descriptions.Item label="卡总成本">{formatPrice(bagDetail.bag_price * bagDetail.bag_count)}元</Descriptions.Item>
          <Descriptions.Item label="卡总售价:福袋总价">{formatPrice(bagDetail.bag_price * bagDetail.bag_count)}元</Descriptions.Item>
        </Descriptions>
      }
      header={{
        extra: [
          <Button key="prev" type="link" onClick={() => {
            // 上操作
          }}>
            <DoubleLeftOutlined />
            上一箱
          </Button>,
          <span>1/10</span>,
          <Button key="next" type="link" onClick={() => {
            // 下操作
          }}>
            <DoubleRightOutlined />
            下一箱
          </Button>
        ]
      }}>

      <EditableProTable
        actionRef={tableRef}
        headerTitle={bagDetail?.bag_name || '福袋详情'}
        columns={columns}
        rowKey="id"
        request={getData}
        params={{
          bag_package_id: Number(params.bag_id),
          bag_id: Number(params.id),
        }}
        toolBarRender={() => {
          return [
            <Button
              type="primary"
              key="save"
              onClick={() => {
                setAddVisible(true);
                getCardList().then(res => {
                  setCardList(res.map(item => ({
                    label: `${item.id} - ${item.card_name}(${item.stock})-(${formatPrice(item.card_sold_price)}元)`,
                    value: item.id,
                  })));
                });

              }}
            >
              新增
            </Button>,
          ];
        }}

        editable={{
          // form,
          // type: 'multiple',
          editableKeys,
          onSave: async (_, rowInfo) => {
            // 将价格从元转换为分后提交
            const submitData = {
              ...rowInfo,
              card_sold_price: yuanToCents(rowInfo.card_sold_price),
              card_price: yuanToCents(rowInfo.card_price),
            };
            return edit(submitData);
          },
          onChange: setEditableRowKeys,
          actionRender: (row, config, dom) => [dom.save, dom.cancel],
        }}

        recordCreatorProps={false}

      />
      <ModalForm
        title="新增"
        layout="horizontal"
        open={addVisible}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAddVisible(false);
          },
        }}
        onFinish={async (value) => {
          // 如果有价格字段，将元转换为分
          const submitData = {
            ...value,
            bag_id: Number(params.id),
            bag_package_id: Number(params.bag_id),
          };

          // 如果表单中有价格字段，进行转换
          if (value.card_sold_price !== undefined) {
            submitData.card_sold_price = yuanToCents(value.card_sold_price);
          }
          if (value.card_price !== undefined) {
            submitData.card_price = yuanToCents(value.card_price);
          }

          add(submitData).then(() => {
            message.success('新增成功');
            setAddVisible(false);
            tableRef.current.reload();
          });
        }}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
      >
        <ProFormSelect name="card_id" label="卡片" width="md" options={cardList} />
        <ProFormDigit
          name="card_num"
          label="数量"
          min={1}          // 最小值
          max={100}        // 最大值
          step={1}           // 步长
          defaultValue={1} // 默认值
          rules={[{ required: true, message: '请选择数量' }]}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default BagDetail;
