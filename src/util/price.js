/**
 * 价格转换工具函数
 */

/**
 * 将分转换为元
 * @param {number|string} cents - 分为单位的价格
 * @returns {number} 元为单位的价格，保留2位小数
 */
export function centsToYuan(cents) {
  if (cents === null || cents === undefined || cents === '') {
    return 0;
  }
  const numCents = Number(cents);
  if (isNaN(numCents)) {
    return 0;
  }
  return Math.round(numCents) / 100;
}

/**
 * 将元转换为分
 * @param {number|string} yuan - 元为单位的价格
 * @returns {number} 分为单位的价格，整数
 */
export function yuanToCents(yuan) {
  if (yuan === null || yuan === undefined || yuan === '') {
    return 0;
  }
  const numYuan = Number(yuan);
  if (isNaN(numYuan)) {
    return 0;
  }
  return Math.round(numYuan * 100);
}

/**
 * 格式化价格显示（分转元，保留2位小数）
 * @param {number|string} cents - 分为单位的价格
 * @returns {string} 格式化后的价格字符串
 */
export function formatPrice(cents) {
  const yuan = centsToYuan(cents);
  return yuan.toFixed(2);
}

/**
 * 格式化价格显示（分转元，去除不必要的小数点）
 * @param {number|string} cents - 分为单位的价格
 * @returns {string} 格式化后的价格字符串
 */
export function formatPriceSimple(cents) {
  const yuan = centsToYuan(cents);
  return yuan % 1 === 0 ? yuan.toString() : yuan.toFixed(2);
}
